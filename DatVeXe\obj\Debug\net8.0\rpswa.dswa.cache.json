{"GlobalPropertiesHash": "IoWGAHLuVaeUdMNrQpeFCjKCqiqCatWnCLE0Y5iaOaU=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["0Tm6fadae6nvMHULw0EIn27DB3gNEyw5oloKBufO1Ew=", "p1Q+TEPk4mmFWEYrEG8mkKQ+Wh8RK3CPZyDdN/dGkUY=", "gbwvyvCSw6vdF/MAC0F9467Vty5DAbvACDH6onsAo9M=", "icV44wUKXAkd5tlv/RSu3eXYt5//VMbnR9EOaUTKdTg=", "yY3tZAgAnMnxmqTosvDFsRIS5lK01ynGST1I571l+CE=", "7oKxmuMIUXDFOZT7QLp6kDhADT7HgjU68v/H5/1K7qY=", "kPUBqO9kpwSpMgl+rV146T/jTdXywHQlymmIPVbpoSM=", "KSPsACiqneyo7Io3S3nWI+2E9VFHOg8/ECSZto7/JYs=", "Nu4fZHWxfbhDO/tTWY4SEW5GbhTiuiKebLS5uAZtKJU=", "ngRkxEmGYzbtYxalshDQrBB0ilbQ7+TIwJRKHhq2KzY=", "wz0Z6zBXRwcHShMV94V1VcRXOJ+ElQ9+gXqB1VEjYr0=", "dZAaILE3ooBvTiU8tglPCKRvLDTGUIpXwTA/WGitnAA=", "o9H5SYOI0vJCXtUrv05Qkt3D6y2EQIzZ/dsO7Ot1wLk=", "4Q3hNyejZPoM7rCLGrpZOnMLkPT+3AqLdmJgCGRc9/4=", "cYcwzsWxpmwzOUW0/1zziKl/uinu/Ddhi5YKbfuV7KQ=", "CsxN+ChmZhdvEHl4ZS66JA8K2KGubvDLGl1TK+SrdfA=", "sAFp9IdVkWl7gAMjVzb3sq5PtpRPWzdls3tQsmQFbX4=", "HOqosr8Vn1ml0xkpbYZRm77dAIXBYN+gRG7xd1bmoRY=", "GsW2qKupDEORCksvqkVL+G5E7xqKtuQsbF0O6gg7R3g=", "8ISmJ3MJbHW+z/ksGKfiT8cEkaFPG9nzuaCbjWaVaQM=", "bXwPKK3ME+gXTAUHvxb+yXvrw6wmU3xsJf6pAlzndkA=", "0oV6hPt2W1r/i0OgZ0KgqEFB1dfv4SiFuelAVejmJIc=", "RAy/jvFm/wbLh02EaHIIko5kTfB8rKi6plxUFlKTlhA=", "3rkDB7znOnHtQQbiQlSEr9UgpBSC+KfwqJZzy1oIMk8=", "HaUIYDH9HOGgF8HUF2TwPB/LEI0WY9dKNMdJ0UYIyYQ=", "6IZJmNSw3R1d+0Dyw3Qx52692plQtFP7k3RFZ3KTCn4=", "TO4dlsBLXC+aLjBd8A3hGX+c9ePAwaj6MOqwzvIZbC0=", "H3xkUtvSOd099rysaKE9Vc1d8YA+d9PR1cdCW1kZV50=", "8yJP+XyK/z2atv1KhrqmH6Fgnxrjlzi+2t+qOe+yPmE=", "tvqRzVYJRUYZNyql/FEzWr0YyRYdrxmdkhNfyq89/Mg=", "GpBuXOXCR9D80qrMsZz3k0noDIPeOg3Y8FXXpiNgGy8=", "aQJ61AFzi0fluhS2Z6/27Jgp+xWGsfo7kvHahRJDGHo=", "Euj00zJT+q2l5pu+P3ehrT/MjmfFQyxKwoyn4TQ3DUs=", "L6KIhVCO7DxzdgVrkECPfbxyI9nCq/2HBchfX9K+p7M=", "EvyZyRkQwIJdT1XcQDrXqld04JuS2DxVVXF5N43tLFc=", "yrkIIOYHAk9+tygIf9eVdYj/ucZlYHENt8g3rRMNWwI=", "2KUDdags8s1HkaPzJWsFojDfdlvBF3m87ReQqHB52ms=", "aLTIBUPiELja1p89DU7ooTea7bBkLfL7Vv6BB+GU/aU=", "MEG4nyj4U0vAIjCyiUevHl78kAu6qMQJfFY2KG6zNps=", "2cppIMwdJkxl1e12DCtEp8bmzt26gmsHhwQp18UMlIo=", "bdaQUOkEEb9umxSZBSuLYA5562O2VFB1YjfM7OhmOvc=", "h/ZS5OP8L3XeaCE2xXobE876ITsTsbb2+Nd+5q8NMgU=", "83Jc5KbEmU2nor0991HStmC76w1Wn20F49uczfdqjmY=", "jizPQD7R0ZGbtMtw7/mWR5jF1idNN1Iy2xGWfAS5Rls=", "P79leQW9RAmb3F2UQ6v4bpf9/CYlbS3rCJsN+bYjsxQ=", "H7wngXPED6LJkFx4bwf5FinkbcMDGY1ZCVDdYw0c58k=", "VUppfGAbzLZ1cuWsMG5CeU7B6nIeYinhvuxaVEYRVrI=", "b9sQfpvPWSJZL2K/hkIxKY4CIoY3SZZS3P7HkcsUOnw=", "ExyZ3ENIuTg6+ExgbmOeoDigfijLGJMJg/xc5x2goU4=", "zEo8uENFygLxJIQV3oUVxDicXpnbrQqqgPKcOy8Tiuc=", "2EyIxy9J/EQenrAOGzqcr5AxS7nK5NbRbFpdp9kgHsQ=", "PyTIz109JhakZbNcgdZXoSl69fTRdjPopVrlAeGwaMM=", "uowWvXrJ2QnhE8DfCI9PKqOsD3z0OSQ+PSucPebfY2A=", "p9axLjYrffCTq5FX+AKC3PN++WMDMGpra0lDTppc1K4=", "RHTijnRq0MMEDUlbbcnSRzbwWcs9RifWKOXeFeezaHY=", "ADAXOdPYGg50w5EIQ+dIfPxsH8p0IjlzSm32vb+6+4Q=", "mb6h3xAEdBLjZMLZM/g/0qhiPLCTB0AOi0/BqZE3Xe0=", "uuXi/C8ofMwuilHHeWao9MYIfjxQdUURl16Y4IJuSZo=", "lT/wVfcXrlngLlYEkWHY5EysipPvLqi7mgv8iHVdb94=", "SH05apiIVYtXQWSi9k74C6V8+Kd+fVVdDzUiEvV3tr8=", "E/CBExh9PtkxSXYYJw1jnMlphpe2lBM/ZbPKXELA4lI=", "hqaJ5EN1Vn0lrLtNhNyKVrNBNSGK/eqvAh2vwJpce+g=", "9gYOx1O0ddO9hQhFz0H1NNaJrkyBSfgdaBRX6JS8bx8=", "ewGCoQvbq2QS/2hK+bfC3tVKk1BrNfMAtK44hbTN/oM=", "i/+uaAo9hEP50B+ki6McIT4iUKrSuC72jq+MRGlyXFo=", "7wMbx6LcQV6mvTMZlEia3TxLD85t60wWCPpZiOKHfnA=", "bdxAQ+XxwAn9n/+4O6esUBgXjxwmJjdJg9UHmKUPC/I=", "0j+YWxhEULz0PQXcxRf6Z0+Q0IQv0Cbm9YhWSZ7N8Uo=", "0PFTuh2xB8gkSpzSZpigVdgJyWckvXp3Xi4FiPbnr/w=", "GySADS8sz5LpM+ktil5lCtduY55ZZLZwAxuj48RMMik=", "R4VkezmOoMzKsoRwbuYX6XpaFpWR/UW0lCHaKEQYpLk=", "5pqYtpjz+CvYKHE5O59KJn2iYTSgOZpQjh2NIBsnswc=", "bW5GgFVSPHglYEwEcwidNMr9fTasn7sWFSBJ91DQL6g=", "SAoUUptf09rEmJ3tx2/WfUwz3eVzCNUocbHH/2D8kt4=", "rPPYpBrN2yAP+SNWb1ICp6Xe8u/+VGHq/uzb66jnYCc=", "QKQNg8nnbMYJ0/A8THx2fKO3IpTgkHcwn8REVF6+AAw=", "+jJAhyvukon2s34zoauvlnLd+DFAnxzj6uuWroBneKA=", "xvZaE/T6ac57w9vrSfJ2KHMuBD0KwzeapSNniLl8Mn4=", "WQi0T+0zmF/0E5iSSkugxWApOWWb7GxAyu04o49MHUQ=", "XIAcktosxE+EYaFIrTD22jkyGWVewj83eiLJKhr7HUo=", "lACFPJWsyDwz2Wwo7bgalljG1m8ePEXfa1vYm6+IulM=", "1RhUwdRJmKDqj4Oo+iVrZ6F3oLevQTLx3wt3K5VF3rw=", "6vOcP93+G6tWEzOv6C2gZMPakazdTWurRnXMdcssAKU=", "ktU2UPlydobI/HRNCyIkLwMH2YTWBY5umLFTmP3FPFQ=", "QDsUB3nidwufgCe5ZEpeKaJ94HrSW2CDtiCmG3leLlI=", "6OuBdgo2jt7dXdQPPD0Nxj06boaXh3Cgy3S5ETfZSDM=", "XQ9KuVkil3urMgXJohZOMWW/oO8Vn9eW+9qan5njpUk=", "Ch02HVbuEppfqxqG4WLSdGkFXvYGMAgM84vB0pUHlWY=", "RdveCmJ+DyvLLo4aIKaguntdVnP15ob0Z5HEOSRQlhU=", "u3BzdJSV+01glISgO3ct7/K1cUzdoFC1ybFedbIq1mg=", "nMfO87LxTH0MNWB2QIrhGGEOl35N3yHOKI/n60kjUXk=", "JvsAsxQVSOVfJAofzXtnNoi6V+1zeAEjGIbqXWeMT9c=", "LBa+6sKoRjDHIQVng/M+S+YqaH8Ra48DjRAetatG8FI=", "OZss3FNs+yVk+N1zMgjVzoMSJGAcFzfn2TNS83h05Ko=", "ktjZXZowpPe8UG5uPXKPp8+rx1j5iRZO4p2u/LdpeZc=", "dso+yx+5I24syGaek7CyRks0dsqslmngu3pOZWg7itc=", "FzoA2tYHbdUa1hNHrhaISE3kA8SnClB8uUFB3UgPclA=", "aC8GPOM4NnvWc87hpNcQAUWtKvvODRPhjKoBVvbtW94=", "yi/e7npegplJA2a/ZeyVlcX/tqzU/+9hyPC3d07Fw2U=", "HHdk5moeEdNdCMvIcjpW4lSE1g5ATgByu+JwV9ZOKeQ=", "6VmvQC3ixGQ+iVqd2XZbfYtveNbwHwAzHETJeM2u/es=", "cBFRPDGtD2V6/cFQSBKjAVwCwBmgp+bya4TaUYvCT+w=", "g60z794p4Sl/inCOJuoSY+YwkQeGp9S/JulKxBvC9jg=", "6Mob5MBjJoptreeZfBdzkZcT8ST1H5iHLqzlmsMBwwg=", "fgbFXdgENpSPFzAHwbGpCHlwvOmZo6Nxfc6bD+RNqKw=", "tvyqR46pntvjbhABJ1Mr89z4Jqu0wUaCio0foDOpbEI=", "5+UdkfdJLOrFoexZ5B3M+8MYoQ+nrLFxrlyyWk0mM7k=", "AIs7pZdLsyAhCtBVqEeKL2X6NC2xaegwjAkT773rXng=", "ipmDr4Wl+V7yxN5AUdamAFblMcqV1RFFIMUS0q1yaAY=", "21iMo8tAOINopWXaIZv5zTS/T5D9XCUNdXpkfdtWJFM=", "IR0UYI66ChMzI1mx60tWi1JsXZDvgxaxTIUqsaqzfMo=", "ar0nAv1ZUwDMX21QRolxW2wb63Jufmq0kTfOEtCjvXM=", "3RCbkA6l9vEPr1awiU7gqUbriPKjdLCl6fmvc5+eEkc=", "0XHKQ6RJ6lqqCnC0JnA7Cap/gzjwTf+3fNB2xw6HpLc=", "ccEz0cIGuj8lyyg6klL45t/O1e/vXIL5rG3x2/FxrV8=", "o9lSHYdby9VlucxyINu9GaYu7wt+t5qTz7CJTMy+u/g=", "Ta7C15V2Pcs8qAkJH2qAJDjZTduAt4YicqnKAjrt8GE=", "g4L4EgqNJEgaZ5PvNHgQGolxBh9LYZjvipXfHhO+N24=", "/e0WUN0LAz5GteYq/GLn45iTc2p0RqLezhd+/lbgL7A=", "HZON8IDImPiA1P61xfY5tCcSNRCMpxOBByZ39D5s4pc=", "st/Bb+xqtnAyrT3bM5ZyFwyGqC2pASH504tAYwK61Gg=", "PRX5ogUI/0fCnSAxHNYlKQ2/Vw/8VOYSTw8iVqL5ABM=", "YkGIQn9Di1HUcLJEm3ARJsLrer77ouLsnxXK6Q77dSY=", "buAhQ/OZTsuElMRVeeCFj9INec4ro8LxZpdR4WbRZ+M=", "RT+gQ02/a9QURpcO/HJ/ZWH3aRbhodgo6kIYdbnkdnA=", "mzZMEqNBdyuQecJUf82tZg1vwoNETqdlU6ZAgYcnuQU=", "qRKkbaDw0zSGfuWtq+/KWREsLPWcifu9gdNWEhcgNdQ=", "I34kHuk0/DNWnDwTgA42lny247Rinw+ma7WvWziMID4=", "CfnxAM+MIERqYa2axScYHMdkZG0UNB8LY8qfsPM3ROg=", "cntewWW/9OnUoqfyfB2QA+yQ8DKnHSRyXSBWFOnMSuw=", "cQIB0rpCwgf97SWRidGGka/D2/ih7tmewiJjFMd2yOg=", "ZcYgndlnjQvH6c/QzMifRHzXYEbeMSKUfvPiDud7G54=", "HOO+yjms9i3UvIX6RyikTmH/nfy2A8mUu9nXP4lziaM=", "npUuAsVTv6uAE6zMzAgZtKKFPd22NNOKxIyP2U/Ootg=", "b0S4rchuujFFP8aAON6vJHnRQaQQvh7Ng113ChvZdW8=", "HY9E73PxOP61jIPuf3dsKI8ahXwnU7VoOUYOFGC/W5g=", "L/xxzSCOGNT5IiOfiCR7o0hVoZAcZFWMrx/6ztjkM0g=", "TlXXq03Bd71UE3WyIod1ZFq35o8Do0NIkrMURiw8Vss=", "4f4cn9yQ0SeY+2GKtDYT4e1UB0ng9xNpkgtzpzsOtmQ=", "Ko7+wfubAbuyRP1mujKhWB8ZKtofHWCACuF1QlpEuhM=", "Pp5uWva9hnQ/BSoKNq7ClR0E7njmWfLti399qesjvRM=", "P+JRdJ0vRKZ0IooOU2hwLPkwQT6eFHUQm8asQQ6hTxA=", "EE3hoE8NlPXx6aIwp8ifXpYy1MHM35kksxe1MXMh2RA=", "KB0KDLA1uZf5uxhMWWLRob/kMlrAMaA424tMwJaVNuY=", "8Zf51JAqebXUsjW4HTlXVd0FQNocuD9tQHssG8bkXdQ=", "Q3+y4C6z6uwkzkCkmeJvtaXO1Sq8gDsDxHPAtj0AXRw=", "311frHAUeBehsqVksizFpvE1Yz4/y12w6jOSJ4m1aZg=", "3UxQuGYPGuSOJxG/rPB8MpKM6B25UXVVi0FOYzF0VXQ=", "+SAd7NVv7MgKY3fWsB+paj16UW/j0dD9ZAQkbLbMLI4=", "x8MOlCAuEa1kWdYdwEGreVsNDDtrg9hIn0FvcosmKZY=", "LEXJB89MvmHOsuPoceYSf6Y4VBSs4OpQ/7YCVy0GgPs=", "Q1KLZ40egj8sNzu7YL9w17EW3s9heiTDklzUGrthGac=", "o0qhFfmPZEJor8h2BzhnommqzgS68a8HN6+Jo4yaC9s=", "nqEdfoQfjKi9csrrAvlXLCuvjPEff+gCZ7ca2pI3CXo=", "w0es44M9CG8xV7vFBm9/yl/giR9PU+pfXcG5Z4GWiHI=", "DnyqC6TDanOalAeu9MkKFUUIrqjW3FKletLyQxqmWIs=", "D3L2Itcayy2so/SrIGOHoXuPwed7cMbFHxdKn/3h/as=", "qJ35LHN6Hkal1PXiF/J+BHJYV/p+L9vu25CarzF5Tdw=", "BS45x7lVbtK0uyc0d3FqvoK88QTyCn1OIwvzhwXEz+A=", "bzeDquCeIqaDrBiTRoCHkaxHPbRcdHXxzDzrSB4a7yk=", "7wLtcHJaTVOJUKQLLp4gZpeYA9g2c3QLdqoSgRI29Wg=", "cMvCANMrHZdLJy/4ozvXPoTlsdGHQxyTqmcmlku4HQg=", "IwuavrKiD88cAu61kEA7gIWAjgScB/vO4aAd/6lbj/4=", "6PEjxrXROMwOdZLZ6jmkUuDshpEr9SmLU2wS19ns0lE=", "yarSl9akNB7H3V8FEuHZscgo8HoUIEofckvIl8kBbuw=", "1EWOZCRuYrRpR1j+Jm4hwp4sBM8HRhC5xbNoCrL4GqU=", "I4g0m36wVkivscPMXlT1RKvLjNfQ+fnXm11lnYW78q8=", "WJzW+5WN+BjiAF+9U9KXmA+4Eaq0EkFFoxS9zHx4/xs=", "/YYoJKGIPtKWwc6IS1mdQG5DI5LIi+5cLyn7BaPos6Y=", "Ly+bCn+X7kJ2/Umu72iyJfuDqwscj31JzPKg9WAY1v0=", "tMZYqGvJAdIcwu3Iob3o/5m9SMt3OPRQmjZ7AI2ODy4=", "MyHtUtPvFGibFDTDYb7dUCsWjlcIjKVQZ0lWkfRvghQ=", "Uxt0G9w2LsQlLeMaCzxE0Ngm8/D5aESauK37UA+AxKc=", "8ZCpdO0XPt1sYwmAjpIxt1JyOKio982pyHP6USqc3A8=", "M6Y+5MdYBjnRV2bPMRizJwmQjXhawIKkTguioBiB0yg=", "1YdzaOrKCktrYg7WLLC1zE7BrYIIMERdx89llTc3aRw=", "UBCSkxnfLSFNujSaeg5r7DwZVCxjn5b9Rxbz7JTGP+Q=", "YVwdUK2mOXjWl4mgQkb0HLpL5pB7bbmvqLibRzBTsyE=", "OO/G7tRSAo97uQ/7T88wrfdxBbTC+RNpjwzEjS5Q4nE=", "2dOiy2jO9/QJL+bza6LR1nPcX6qW0plKLTMjL1yC+mU=", "FwBHenxZbyj5GfbVNpvIq6PUpSktZ4ps616VgbWYo0o=", "D9AQjABEWNnDziEZsIgz122WYGPNygd+4DRy4kGb2zk=", "xh7T0Lq2nJRGswN55uiTRwkkXWWJHJW20clY6CQgY4g=", "nptpl33hPnNsQg2IKVUaGK88bYEZ34GvbSgBa8i6FqM=", "WyGeGZn10YhQ2yT7txUmqxWDtlLC1TybC7Tu3Kpsiis=", "JXqjwskIwq+VKmu4wZEZYBgrY6Ehzi7mbEf0gQL2UwI=", "zr9rtOHBW97DSCSjpmPzw57zxAlnIa3qhB44GT4Ma4w=", "IUZnMn1vYibwsZSmzfDfkD/hGnMYnZZgz9pKzGxyqkk=", "X2a91VMPx2cuNCJVn2IbtNeTgrrIwUTwt9a3cABgcbo=", "CWFR+hE6sqR4ivQ8RD+rqzdvifZnig1SKAwtG8HwkQ0=", "gFiECskoVx0l5f82/dD0bi+ugGbAPHqATjSvzJDg8pM=", "gDooJOpvuQNRjpVQ+MoHppBrjVRK5Wdvdido7N9TzB8=", "IEdnhVK5Pe72RpWFCZT58dwlZ7VrBzXdffJn06a/VYY=", "0ff5sMZ1B0fajriHF9FvKQMBpkN12Y6glEPyoda6EYQ=", "3WUlIhQcLZwTRcJqqWs4Zu0qWJ+RL0XfQ65uBI+wXio=", "HM3ydj+Ff+dyGglbKRL6O7xdnC4LMOXRZptcejfoxgA=", "lCCmX0QZYKR6jv5HD5I8j/m5MpXqjVvuGp2QiLGWy+4=", "MtA4SMsNBgnf42bE8gi4gguNYH0BAZ8tomkObncAEj8=", "00ac+c8EOILgT52YdoUt6c4xN5slxUwFN+iVeLbKHqU="], "CachedAssets": {"0Tm6fadae6nvMHULw0EIn27DB3gNEyw5oloKBufO1Ew=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\admin.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/admin#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "a5qpn6nwqe", "Integrity": "YvCbOf9qEAOfXt2Bj2DkpVbwPfcAI8+sXrzUCDaJ9wY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\admin.css", "FileLength": 10555, "LastWriteTime": "2025-06-17T14:00:43.9387761+00:00"}, "p1Q+TEPk4mmFWEYrEG8mkKQ+Wh8RK3CPZyDdN/dGkUY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\color-fixes.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/color-fixes#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "avs8i5688v", "Integrity": "2tV2jS+o19m1SbUqyHwH11jlJwv36BwPT8l5XuKR6AY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\color-fixes.css", "FileLength": 9464, "LastWriteTime": "2025-06-12T03:27:05.5565859+00:00"}, "gbwvyvCSw6vdF/MAC0F9467Vty5DAbvACDH6onsAo9M=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\page-specific-fixes.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/page-specific-fixes#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s6so21365t", "Integrity": "tDpLQbwrv+9m63a6Yf8ym5DHvNDHPNYK5wZ24P4w4hA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\page-specific-fixes.css", "FileLength": 9930, "LastWriteTime": "2025-06-12T03:28:05.6420101+00:00"}, "icV44wUKXAkd5tlv/RSu3eXYt5//VMbnR9EOaUTKdTg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\css\\site.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x3u8v4r6s1", "Integrity": "bYlH71aSsfpd0Ikxr8yTBvambNffcRJrKAuQ0Yo741c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 14625, "LastWriteTime": "2025-06-12T03:28:55.6781086+00:00"}, "yY3tZAgAnMnxmqTosvDFsRIS5lK01ynGST1I571l+CE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\BusBanner.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/BusBanner#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wswepo8j2e", "Integrity": "cAnkDCCa7L/Aave23u03hBPCxIRe4f6RE3To2FKuZHk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\BusBanner.jpg", "FileLength": 412962, "LastWriteTime": "2025-05-29T16:53:47.7035087+00:00"}, "7oKxmuMIUXDFOZT7QLp6kDhADT7HgjU68v/H5/1K7qY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancity.webp", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/mancity#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cuj6g20mhu", "Integrity": "DH5JH9mMrWeeWzwMPmw/ViJKk9xKSC+vp0gum9lIKVk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\mancity.webp", "FileLength": 518686, "LastWriteTime": "2025-05-30T18:28:16.8728415+00:00"}, "kPUBqO9kpwSpMgl+rV146T/jTdXywHQlymmIPVbpoSM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\mancitybus.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/mancitybus#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "p3yf6yxrqx", "Integrity": "m0cRtQDSNpPR+ACUJaDKBr9p/pVGycQPH9X1M+O0UaA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\mancitybus.jpg", "FileLength": 162920, "LastWriteTime": "2025-05-30T18:33:06.2146025+00:00"}, "KSPsACiqneyo7Io3S3nWI+2E9VFHOg8/ECSZto7/JYs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\readmarid.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/readmarid#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "qmrga2uz31", "Integrity": "Ht4gl3yj+neQ8mnCCGrl45M8AcNj7jKzkDyjcCswUQ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\readmarid.jpg", "FileLength": 114373, "LastWriteTime": "2025-06-06T04:00:01.2177346+00:00"}, "Nu4fZHWxfbhDO/tTWY4SEW5GbhTiuiKebLS5uAZtKJU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\images\\realvscity.jpg", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "images/realvscity#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "1ydqed4cvh", "Integrity": "59RxYW9+ZsTMhwpGhZpIcmr4H7a5JMUifRytRo4jABI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\images\\realvscity.jpg", "FileLength": 56258, "LastWriteTime": "2025-06-12T03:17:05.6266269+00:00"}, "ngRkxEmGYzbtYxalshDQrBB0ilbQ7+TIwJRKHhq2KzY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\admin.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/admin#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "etsnhtcic7", "Integrity": "XolJhSBU5njpOhuk2k1Crn3aOrQLTyqIodcN8riL0oM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\admin.js", "FileLength": 13289, "LastWriteTime": "2025-06-16T18:19:07.291451+00:00"}, "wz0Z6zBXRwcHShMV94V1VcRXOJ+ElQ9+gXqB1VEjYr0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\color-contrast-checker.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/color-contrast-checker#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9qy6rw296h", "Integrity": "UdCV8Ngi9MF71ly/M6Aiho7ctndl/MHgXH9ekj7SUeQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\color-contrast-checker.js", "FileLength": 11054, "LastWriteTime": "2025-06-12T03:29:44.1086413+00:00"}, "dZAaILE3ooBvTiU8tglPCKRvLDTGUIpXwTA/WGitnAA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\site.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-05-29T16:25:30.0858949+00:00"}, "o9H5SYOI0vJCXtUrv05Qkt3D6y2EQIzZ/dsO7Ot1wLk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\js\\user-management.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "js/user-management#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "z797e9e25e", "Integrity": "bxyY72CMqX09sfC+tW33vX61fEqryz0r8YPyUmO63Ao=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\user-management.js", "FileLength": 7153, "LastWriteTime": "2025-06-15T13:31:56.5626835+00:00"}, "4Q3hNyejZPoM7rCLGrpZOnMLkPT+3AqLdmJgCGRc9/4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-05-29T16:25:30.1478105+00:00"}, "cYcwzsWxpmwzOUW0/1zziKl/uinu/Ddhi5YKbfuV7KQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "CsxN+ChmZhdvEHl4ZS66JA8K2KGubvDLGl1TK+SrdfA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "sAFp9IdVkWl7gAMjVzb3sq5PtpRPWzdls3tQsmQFbX4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "HOqosr8Vn1ml0xkpbYZRm77dAIXBYN+gRG7xd1bmoRY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-05-29T16:25:30.1502121+00:00"}, "GsW2qKupDEORCksvqkVL+G5E7xqKtuQsbF0O6gg7R3g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-05-29T16:25:30.1549208+00:00"}, "8ISmJ3MJbHW+z/ksGKfiT8cEkaFPG9nzuaCbjWaVaQM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "bXwPKK3ME+gXTAUHvxb+yXvrw6wmU3xsJf6pAlzndkA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "0oV6hPt2W1r/i0OgZ0KgqEFB1dfv4SiFuelAVejmJIc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "RAy/jvFm/wbLh02EaHIIko5kTfB8rKi6plxUFlKTlhA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-05-29T16:25:30.1617274+00:00"}, "3rkDB7znOnHtQQbiQlSEr9UgpBSC+KfwqJZzy1oIMk8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "HaUIYDH9HOGgF8HUF2TwPB/LEI0WY9dKNMdJ0UYIyYQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "6IZJmNSw3R1d+0Dyw3Qx52692plQtFP7k3RFZ3KTCn4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "TO4dlsBLXC+aLjBd8A3hGX+c9ePAwaj6MOqwzvIZbC0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "H3xkUtvSOd099rysaKE9Vc1d8YA+d9PR1cdCW1kZV50=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-05-29T16:25:30.1662367+00:00"}, "8yJP+XyK/z2atv1KhrqmH6Fgnxrjlzi+2t+qOe+yPmE=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-05-29T16:25:30.1707744+00:00"}, "tvqRzVYJRUYZNyql/FEzWr0YyRYdrxmdkhNfyq89/Mg=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-05-29T16:25:30.1707744+00:00"}, "GpBuXOXCR9D80qrMsZz3k0noDIPeOg3Y8FXXpiNgGy8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-05-29T16:25:30.1752808+00:00"}, "aQJ61AFzi0fluhS2Z6/27Jgp+xWGsfo7kvHahRJDGHo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-05-29T16:25:30.1752808+00:00"}, "Euj00zJT+q2l5pu+P3ehrT/MjmfFQyxKwoyn4TQ3DUs=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-05-29T16:25:30.1769657+00:00"}, "L6KIhVCO7DxzdgVrkECPfbxyI9nCq/2HBchfX9K+p7M=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-05-29T16:25:30.1769657+00:00"}, "EvyZyRkQwIJdT1XcQDrXqld04JuS2DxVVXF5N43tLFc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "yrkIIOYHAk9+tygIf9eVdYj/ucZlYHENt8g3rRMNWwI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "2KUDdags8s1HkaPzJWsFojDfdlvBF3m87ReQqHB52ms=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "aLTIBUPiELja1p89DU7ooTea7bBkLfL7Vv6BB+GU/aU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-05-29T16:25:30.1809721+00:00"}, "MEG4nyj4U0vAIjCyiUevHl78kAu6qMQJfFY2KG6zNps=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-05-29T16:25:30.1871172+00:00"}, "2cppIMwdJkxl1e12DCtEp8bmzt26gmsHhwQp18UMlIo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-05-29T16:25:30.1871172+00:00"}, "bdaQUOkEEb9umxSZBSuLYA5562O2VFB1YjfM7OhmOvc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-05-29T16:25:30.1911245+00:00"}, "h/ZS5OP8L3XeaCE2xXobE876ITsTsbb2+Nd+5q8NMgU=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-05-29T16:25:30.1963316+00:00"}, "83Jc5KbEmU2nor0991HStmC76w1Wn20F49uczfdqjmY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-05-29T16:25:30.2009634+00:00"}, "jizPQD7R0ZGbtMtw7/mWR5jF1idNN1Iy2xGWfAS5Rls=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-05-29T16:25:30.2014891+00:00"}, "P79leQW9RAmb3F2UQ6v4bpf9/CYlbS3rCJsN+bYjsxQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-05-29T16:25:30.2077248+00:00"}, "H7wngXPED6LJkFx4bwf5FinkbcMDGY1ZCVDdYw0c58k=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-05-29T16:25:30.209978+00:00"}, "VUppfGAbzLZ1cuWsMG5CeU7B6nIeYinhvuxaVEYRVrI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-05-29T16:25:30.2160665+00:00"}, "b9sQfpvPWSJZL2K/hkIxKY4CIoY3SZZS3P7HkcsUOnw=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-05-29T16:25:30.2165938+00:00"}, "ExyZ3ENIuTg6+ExgbmOeoDigfijLGJMJg/xc5x2goU4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-05-29T16:25:30.218908+00:00"}, "zEo8uENFygLxJIQV3oUVxDicXpnbrQqqgPKcOy8Tiuc=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-05-29T16:25:30.218908+00:00"}, "2EyIxy9J/EQenrAOGzqcr5AxS7nK5NbRbFpdp9kgHsQ=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-05-29T16:25:30.2210954+00:00"}, "PyTIz109JhakZbNcgdZXoSl69fTRdjPopVrlAeGwaMM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-05-29T16:25:30.2216845+00:00"}, "uowWvXrJ2QnhE8DfCI9PKqOsD3z0OSQ+PSucPebfY2A=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-05-29T16:25:30.2614085+00:00"}, "p9axLjYrffCTq5FX+AKC3PN++WMDMGpra0lDTppc1K4=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-05-29T16:25:30.2614085+00:00"}, "RHTijnRq0MMEDUlbbcnSRzbwWcs9RifWKOXeFeezaHY=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-05-29T16:25:30.2659992+00:00"}, "ADAXOdPYGg50w5EIQ+dIfPxsH8p0IjlzSm32vb+6+4Q=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-05-29T16:25:30.3060225+00:00"}, "mb6h3xAEdBLjZMLZM/g/0qhiPLCTB0AOi0/BqZE3Xe0=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-05-29T16:25:30.3151027+00:00"}, "uuXi/C8ofMwuilHHeWao9MYIfjxQdUURl16Y4IJuSZo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-29T16:25:30.3009598+00:00"}, "lT/wVfcXrlngLlYEkWHY5EysipPvLqi7mgv8iHVdb94=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-05-29T16:25:30.3377384+00:00"}, "SH05apiIVYtXQWSi9k74C6V8+Kd+fVVdDzUiEvV3tr8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-05-29T16:25:30.3458131+00:00"}, "E/CBExh9PtkxSXYYJw1jnMlphpe2lBM/ZbPKXELA4lI=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-05-29T16:25:30.3458131+00:00"}, "hqaJ5EN1Vn0lrLtNhNyKVrNBNSGK/eqvAh2vwJpce+g=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-05-29T16:25:30.1398631+00:00"}, "9gYOx1O0ddO9hQhFz0H1NNaJrkyBSfgdaBRX6JS8bx8=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-05-29T16:25:30.1433851+00:00"}, "ewGCoQvbq2QS/2hK+bfC3tVKk1BrNfMAtK44hbTN/oM=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-05-29T16:25:30.1448272+00:00"}, "i/+uaAo9hEP50B+ki6McIT4iUKrSuC72jq+MRGlyXFo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-05-29T16:25:30.1458397+00:00"}, "7wMbx6LcQV6mvTMZlEia3TxLD85t60wWCPpZiOKHfnA=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-29T16:25:30.3166531+00:00"}, "bdxAQ+XxwAn9n/+4O6esUBgXjxwmJjdJg9UHmKUPC/I=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-05-29T16:25:30.136091+00:00"}, "0j+YWxhEULz0PQXcxRf6Z0+Q0IQv0Cbm9YhWSZ7N8Uo=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-05-29T16:25:30.136091+00:00"}, "0PFTuh2xB8gkSpzSZpigVdgJyWckvXp3Xi4FiPbnr/w=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-05-29T16:25:30.1398631+00:00"}, "GySADS8sz5LpM+ktil5lCtduY55ZZLZwAxuj48RMMik=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-05-29T16:25:30.3112225+00:00"}, "R4VkezmOoMzKsoRwbuYX6XpaFpWR/UW0lCHaKEQYpLk=": {"Identity": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\test-routes.html", "SourceId": "DatVeXe", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Documents\\Zalo Received Files\\Đặt vé xe\\DatVeXe\\wwwroot\\", "BasePath": "_content/DatVeXe", "RelativePath": "test-routes#[.{fingerprint}]?.html", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nne9mve8jp", "Integrity": "c0TA3uf1rDYask4SWDqDgDceep+WGXscRu6tdkX/xrk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\test-routes.html", "FileLength": 440, "LastWriteTime": "2025-06-15T13:31:56.6294491+00:00"}}, "CachedCopyCandidates": {}}